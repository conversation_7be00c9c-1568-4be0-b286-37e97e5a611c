#!/bin/bash

# Test CI Build Script
# This script simulates the GitLab CI build process locally for testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="julianhandl-website"
NODE_VERSION="22"
TEST_TAG="local-test"

echo -e "${BLUE}🧪 Testing GitLab CI Build Process Locally${NC}"
echo "=================================================="

# Function to print step headers
print_step() {
    echo -e "\n${YELLOW}📋 Step: $1${NC}"
    echo "----------------------------------------"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_step "Checking Prerequisites"

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

if ! command_exists node; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is available${NC}"
echo -e "${GREEN}✅ Node.js is available${NC}"

# Check Node.js version
NODE_CURRENT=$(node --version | sed 's/v//')
echo "Current Node.js version: $NODE_CURRENT"
echo "CI Node.js version: $NODE_VERSION"

# Step 1: Lint (simulate CI lint stage)
print_step "Linting Code"

if [ -f "package.json" ]; then
    echo "Installing dependencies..."
    npm ci --prefer-offline --no-audit
    
    echo "Running linter..."
    npm run lint
    echo -e "${GREEN}✅ Linting passed${NC}"
else
    echo -e "${RED}❌ package.json not found${NC}"
    exit 1
fi

# Step 2: Test Build (simulate CI test stage)
print_step "Testing Build"

echo "Building Next.js application..."
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build successful${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Step 3: Docker Build (simulate CI build stage)
print_step "Building Docker Image"

echo "Building Docker image with tag: $PROJECT_NAME:$TEST_TAG"

# Build with same parameters as CI
docker build \
    --tag "$PROJECT_NAME:$TEST_TAG" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker build successful${NC}"
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

# Step 4: Test Docker Image
print_step "Testing Docker Image"

echo "Starting container for testing..."

# Start container in background
CONTAINER_ID=$(docker run -d -p 3001:3000 "$PROJECT_NAME:$TEST_TAG")

if [ $? -eq 0 ]; then
    echo "Container started with ID: $CONTAINER_ID"
    
    # Wait for container to be ready
    echo "Waiting for application to start..."
    sleep 10
    
    # Test if application is responding
    if command_exists curl; then
        echo "Testing application response..."
        if curl -f http://localhost:3001 >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Application is responding${NC}"
        else
            echo -e "${YELLOW}⚠️  Application might not be ready yet${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  curl not available, skipping response test${NC}"
    fi
    
    # Stop and remove container
    echo "Stopping test container..."
    docker stop "$CONTAINER_ID" >/dev/null
    docker rm "$CONTAINER_ID" >/dev/null
    
    echo -e "${GREEN}✅ Container test completed${NC}"
else
    echo -e "${RED}❌ Failed to start container${NC}"
    exit 1
fi

# Step 5: Image Analysis
print_step "Image Analysis"

echo "Docker image details:"
docker images "$PROJECT_NAME:$TEST_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

echo -e "\nImage layers:"
docker history "$PROJECT_NAME:$TEST_TAG" --format "table {{.CreatedBy}}\t{{.Size}}" | head -10

# Step 6: Cleanup
print_step "Cleanup"

echo "Removing test image..."
docker rmi "$PROJECT_NAME:$TEST_TAG" >/dev/null 2>&1

echo -e "${GREEN}✅ Cleanup completed${NC}"

# Summary
echo -e "\n${BLUE}🎉 CI Build Test Summary${NC}"
echo "=================================================="
echo -e "${GREEN}✅ All steps completed successfully${NC}"
echo ""
echo "Your project is ready for GitLab CI/CD pipeline!"
echo ""
echo "Next steps:"
echo "1. Commit the .gitlab-ci.yml file"
echo "2. Push to GitLab to trigger the pipeline"
echo "3. Monitor the pipeline in GitLab CI/CD interface"
echo ""
echo -e "${YELLOW}💡 Tip: You can run this script anytime to test changes locally${NC}"
