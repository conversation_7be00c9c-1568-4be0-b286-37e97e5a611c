# GitLab CI/CD Pipeline Setup Guide

This document explains the GitLab CI/CD pipeline configuration for the Julian Handl website project.

## Overview

The pipeline automatically builds, tests, and deploys the Next.js application to GitLab Container Registry. It includes:

- **Linting**: Code quality checks using ESLint
- **Testing**: Build verification (can be extended with actual tests)
- **Docker Build**: Multi-stage optimized Docker image creation
- **Registry Push**: Automatic image publishing to GitLab Container Registry
- **Deployment Instructions**: Automated generation of deployment guides

## Pipeline Stages

### 1. Lint Stage
- **install_dependencies**: Installs and caches npm dependencies
- **lint**: Runs ESLint to check code quality

### 2. Test Stage
- **test_build**: Verifies the application builds successfully
- Artifacts: Saves `.next/` build output for 1 hour

### 3. Build Stage
- **build_image**: Builds and pushes Docker image (main branch and tags)
- **build_image_mr**: Builds MR-specific images for merge requests

### 4. Deploy Stage
- **deploy_production**: Creates deployment instructions
- **container_scanning**: Security scanning (GitLab Ultimate feature)
- **cleanup_registry**: Manual registry cleanup job

## Triggers

The pipeline runs on:
- **Merge Requests**: Linting, testing, and MR-specific image builds
- **Main Branch**: Full pipeline including production image builds
- **Tags**: Full pipeline for versioned releases

## Required GitLab CI/CD Variables

The pipeline uses built-in GitLab variables:
- `$CI_REGISTRY`: GitLab Container Registry URL
- `$CI_REGISTRY_USER`: Registry username (automatic)
- `$CI_REGISTRY_PASSWORD`: Registry password (automatic)
- `$CI_REGISTRY_IMAGE`: Full image path (automatic)

No additional variables need to be configured.

## Docker Image Tags

- **Latest**: `$CI_REGISTRY_IMAGE:latest` (main branch only)
- **Branch/Tag**: `$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG`
- **Merge Request**: `$CI_REGISTRY_IMAGE:mr-$CI_MERGE_REQUEST_IID`
- **Cache**: `$CI_REGISTRY_IMAGE:cache` (for build optimization)

## Caching Strategy

### Node.js Dependencies
- Cache key based on `package-lock.json` hash
- Cached paths: `.npm/` and `node_modules/`
- Separate push/pull policies for optimization

### Docker Layer Caching
- Uses `--cache-from` with registry cache image
- BuildKit inline cache for efficient layer reuse
- Separate cache image maintained automatically

## Security Features

### Container Registry Authentication
- Automatic authentication using GitLab CI tokens
- No manual credential configuration required

### Image Security
- Multi-stage builds minimize attack surface
- Non-root user in final image
- Security scanning integration (GitLab Ultimate)

### Build Isolation
- Each job runs in isolated containers
- No persistent state between pipeline runs

## Performance Optimizations

### Build Speed
- Efficient dependency caching
- Docker layer caching from registry
- Parallel job execution where possible

### Registry Storage
- Manual cleanup job for old images
- Cache layer management
- MR-specific image cleanup

## Usage Examples

### Pulling Images

```bash
# Pull latest image
docker pull $CI_REGISTRY_IMAGE:latest

# Pull specific version
docker pull $CI_REGISTRY_IMAGE:v1.0.0

# Pull MR image for testing
docker pull $CI_REGISTRY_IMAGE:mr-123
```

### Running Containers

```bash
# Basic run
docker run -p 3000:3000 $CI_REGISTRY_IMAGE:latest

# With data persistence
docker run -d \
  --name julianhandl-website \
  -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  --restart unless-stopped \
  $CI_REGISTRY_IMAGE:latest
```

### Docker Compose Integration

```yaml
version: '3.8'
services:
  website:
    image: $CI_REGISTRY_IMAGE:latest
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    environment:
      - NODE_ENV=production
```

## Extending the Pipeline

### Adding Tests

To add actual test scripts:

1. Add test script to `package.json`:
```json
{
  "scripts": {
    "test": "jest",
    "test:ci": "jest --ci --coverage"
  }
}
```

2. Update the pipeline:
```yaml
test_unit:
  <<: *node_job
  <<: *node_cache
  stage: test
  script:
    - npm run test:ci
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
```

### Adding Environment-Specific Deployments

```yaml
deploy_staging:
  extends: deploy_production
  environment:
    name: staging
    url: https://staging.julianhandl.at
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

deploy_production:
  environment:
    name: production
    url: https://julianhandl.at
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - when: manual
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify package-lock.json is committed
   - Review build logs for dependency issues

2. **Registry Authentication**
   - Ensure GitLab Container Registry is enabled
   - Check project permissions for registry access

3. **Cache Issues**
   - Clear pipeline cache if dependencies are corrupted
   - Verify cache key generation from package-lock.json

### Monitoring

- Pipeline status: GitLab CI/CD → Pipelines
- Registry usage: GitLab Packages & Registries → Container Registry
- Build artifacts: Pipeline → Job → Browse artifacts

## Best Practices

1. **Version Tagging**: Use semantic versioning for releases
2. **Branch Protection**: Require MR approval before merging
3. **Registry Cleanup**: Regularly clean old images to save storage
4. **Security Updates**: Keep base images and dependencies updated
5. **Monitoring**: Set up alerts for pipeline failures

## Support

For pipeline issues:
1. Check GitLab CI/CD documentation
2. Review pipeline logs in GitLab interface
3. Verify Docker and Node.js compatibility
4. Test builds locally using the same Dockerfile
