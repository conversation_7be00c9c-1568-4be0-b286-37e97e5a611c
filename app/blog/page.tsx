import { lstatSync, readdirSync } from "fs";

import Link from "next/link";
import path from "path";

const blogFolderName = "blog";

const blogDirectory = path.join(process.cwd(), "app", blogFolderName);

export default function Blog() {
  const blogPostDirectories = readdirSync(blogDirectory).filter((folderName) =>
    lstatSync(path.join(blogDirectory, folderName)).isDirectory()
  );

  const blogPosts = blogPostDirectories
    .map((folderName) => {
      const {
        metadata,
      } = require(`@/app/${blogFolderName}/${folderName}/page.mdx`);

      return {
        slug: folderName,
        metadata,
      };
    })
    .map(({ slug, metadata }) => ({
      slug,
      href: `/${blogFolderName}/${slug}`,
      post: metadata,
    }));

  return (
    <div className="mx-auto max-w-7xl px-6 lg:px-8">
      <div className="mx-auto max-w-2xl">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">
          Blog of <PERSON>
        </h1>
        <p className="mt-2 text-lg leading-8 text-slate-400">
          {`I'm interested in a lot of things. In some of them I'm even good at.
          In this blog I'm mostly writing about architecture, leadership and
          coding in general.
          `}
        </p>
        <div className="mt-10 space-y-16 border-t border-gray-200 pt-10 sm:mt-16 sm:pt-16">
          {blogPosts.map(({ post, slug, href }) => (
            <article
              key={slug}
              className="flex max-w-xl flex-col items-start justify-between"
            >
              {/* TODO: Correct dateTime */}
              <time dateTime={post.date} className="text-slate-300 text-xs">
                {post.date}
              </time>
              <div className="group relative">
                <h3 className="mt-3 text-lg font-semibold leading-6">
                  <Link href={href}>{post.title}</Link>
                </h3>
                <p className="mt-5 line-clamp-3 text-sm leading-6 text-slate-300">
                  {post.description}
                </p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </div>
  );
}
