# GitLab CI/CD Pipeline for Julian Handl Website
# This pipeline builds, tests, and deploys a Next.js application to GitLab Container Registry

# Define stages
stages:
  - lint
  - test
  - build
  - deploy

# Global variables
variables:
  # Docker image settings
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # Container registry settings
  REGISTRY_IMAGE: $CI_REGISTRY_IMAGE
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  
  # Node.js settings
  NODE_VERSION: "22"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"
  
  # Build optimization
  NEXT_TELEMETRY_DISABLED: "1"

# Cache configuration for faster builds
.node_cache: &node_cache
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/
      - node_modules/
    policy: pull

.node_cache_push: &node_cache_push
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/
      - node_modules/
    policy: push

# Base job template for Node.js jobs
.node_job: &node_job
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache git
    - npm ci --cache .npm --prefer-offline --no-audit

# Lint stage - Check code quality
lint:
  <<: *node_job
  <<: *node_cache
  stage: lint
  script:
    - npm run lint
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Install dependencies (for cache warming)
install_dependencies:
  <<: *node_job
  <<: *node_cache_push
  stage: lint
  script:
    - echo "Dependencies installed and cached"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Test stage - Run tests (currently using build as test since no test script exists)
test_build:
  <<: *node_job
  <<: *node_cache
  stage: test
  script:
    - npm run build
  artifacts:
    paths:
      - .next/
    expire_in: 1 hour
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Build Docker image
build_image:
  stage: build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_BUILDKIT: 1
  before_script:
    # Login to GitLab Container Registry
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    # Build with cache from registry
    - |
      docker build \
        --cache-from $REGISTRY_IMAGE:cache \
        --tag $REGISTRY_IMAGE:$IMAGE_TAG \
        --tag $REGISTRY_IMAGE:latest \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        .
    
    # Push images to registry
    - docker push $REGISTRY_IMAGE:$IMAGE_TAG
    - docker push $REGISTRY_IMAGE:latest
    
    # Push cache layer
    - docker tag $REGISTRY_IMAGE:latest $REGISTRY_IMAGE:cache
    - docker push $REGISTRY_IMAGE:cache
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Build image for merge requests (without pushing to latest)
build_image_mr:
  stage: build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_BUILDKIT: 1
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    # Build with cache from registry
    - |
      docker build \
        --cache-from $REGISTRY_IMAGE:cache \
        --tag $REGISTRY_IMAGE:mr-$CI_MERGE_REQUEST_IID \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        .
    
    # Push MR-specific image
    - docker push $REGISTRY_IMAGE:mr-$CI_MERGE_REQUEST_IID
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Deploy stage - Create deployment artifacts and instructions
deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    # Create deployment instructions
    - |
      cat > deployment-instructions.md << EOF
      # Deployment Instructions

      ## Docker Image
      - **Registry**: $CI_REGISTRY_IMAGE
      - **Tag**: $IMAGE_TAG
      - **Full Image**: $REGISTRY_IMAGE:$IMAGE_TAG

      ## Pull and Run Commands
      \`\`\`bash
      # Pull the image
      docker pull $REGISTRY_IMAGE:$IMAGE_TAG

      # Run the container
      docker run -d \\
        --name julianhandl-website \\
        -p 3000:3000 \\
        -v \$(pwd)/data:/app/data \\
        --restart unless-stopped \\
        $REGISTRY_IMAGE:$IMAGE_TAG
      \`\`\`

      ## Docker Compose
      Update your docker-compose.yml:
      \`\`\`yaml
      version: '3.8'
      services:
        website:
          image: $REGISTRY_IMAGE:$IMAGE_TAG
          ports:
            - "3000:3000"
          volumes:
            - ./data:/app/data
          restart: unless-stopped
          environment:
            - NODE_ENV=production
      \`\`\`

      ## Health Check
      \`\`\`bash
      curl http://localhost:3000/api/data/weather?limit=1
      \`\`\`

      Built from commit: $CI_COMMIT_SHA
      Pipeline: $CI_PIPELINE_URL
      EOF

    - echo "Deployment instructions created"
    - cat deployment-instructions.md
  artifacts:
    paths:
      - deployment-instructions.md
    expire_in: 30 days
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Security scanning (optional, requires GitLab Ultimate)
container_scanning:
  stage: deploy
  variables:
    CS_IMAGE: $REGISTRY_IMAGE:$IMAGE_TAG
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
  allow_failure: true

# Cleanup old images (optional job to manage registry storage)
cleanup_registry:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "Registry cleanup would run here"
    - echo "Consider implementing cleanup of old MR images and cache layers"
    - echo "You can use GitLab API to delete old container images"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - when: manual
  allow_failure: true
